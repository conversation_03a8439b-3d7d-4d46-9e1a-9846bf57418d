<template>
  <div class="container mx-auto p-6 max-w-4xl">
    <div class="mb-8">
      <h1 class="text-3xl font-bold">{{ t('PROFILE') }}</h1>
      <p class="text-muted-foreground mt-2">{{ t('MY_ACCOUNT') }}</p>
    </div>

    <Tabs default-value="information" class="w-full">
      <TabsList class="grid w-full grid-cols-2">
        <TabsTrigger value="information">{{ t('INFORMATION') }}</TabsTrigger>
        <TabsTrigger value="settings">{{ t('SETTINGS') }}</TabsTrigger>
      </TabsList>

      <!-- Information Tab -->
      <TabsContent value="information" class="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>{{ t('PERSONAL_INFORMATION') }}</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <Label>{{ t('USER_ID') }}</Label>
                <Input :value="authStore.user?.id?.toString() || ''" readonly class="bg-muted" />
              </div>
              <div class="space-y-2">
                <Label>{{ t('FIRST_NAME') }}</Label>
                <Input :value="authStore.user?.first_name || ''" readonly class="bg-muted" />
              </div>
              <div class="space-y-2">
                <Label>{{ t('LAST_NAME') }}</Label>
                <Input :value="authStore.user?.last_name || ''" readonly class="bg-muted" />
              </div>
              <div class="space-y-2">
                <Label>{{ t('EMAIL') }}</Label>
                <Input :value="authStore.user?.email || ''" readonly class="bg-muted" />
              </div>
            </div>
            <div class="space-y-2">
              <Label>{{ t('MEMBER_SINCE') }}</Label>
              <Input :value="getUploadDate(authStore.user?.created_at || '', 'dd/mm/yyyy HH:MM')" readonly class="bg-muted" />
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- Settings Tab -->
      <TabsContent value="settings" class="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>{{ t('PREFERENCES') }}</CardTitle>
          </CardHeader>
          <CardContent class="space-y-6">
            <!-- Dark Mode Toggle -->
            <div class="flex items-center justify-between">
              <div class="space-y-0.5">
                <Label>{{ t('DARK_MODE') }}</Label>
                <p class="text-sm text-muted-foreground">{{ t('DARK_MODE_DESCRIPTION') }}</p>
              </div>
              <SwitchRoot :checked="isDark" @update:checked="toggleDark" class="peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50">
                <SwitchThumb class="bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0" />
              </SwitchRoot>
            </div>

            <!-- Language Selector -->
            <div class="space-y-2">
              <Label>{{ t('LANGUAGE') }}</Label>
              <p class="text-sm text-muted-foreground">{{ t('LANGUAGE_DESCRIPTION') }}</p>
              <Select v-model="selectedLanguage" @update:model-value="changeLanguage">
                <SelectTrigger class="w-full md:w-[200px]">
                  <SelectValue :placeholder="t('LANGUAGE')" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="en">{{ t('ENGLISH') }}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        <!-- Danger Zone -->
        <Card class="border-destructive">
          <CardHeader>
            <CardTitle class="text-destructive">{{ t('DANGER_ZONE') }}</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="space-y-2">
              <p class="text-sm text-muted-foreground">{{ t('ERASE_DATA_DESCRIPTION') }}</p>
              <Dialog v-model:open="showDeleteDialog">
                <DialogTrigger as-child>
                  <Button variant="destructive">{{ t('ERASE_MY_DATA') }}</Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>{{ t('CONFIRM_DELETE_ACCOUNT') }}</DialogTitle>
                    <DialogDescription>
                      {{ t('DELETE_ACCOUNT_WARNING') }}
                    </DialogDescription>
                  </DialogHeader>
                  <DialogFooter>
                    <Button variant="outline" @click="showDeleteDialog = false">{{ t('CANCEL') }}</Button>
                    <Button variant="destructive" @click="deleteAccount" :disabled="isDeleting">
                      {{ isDeleting ? t('LOADING') : t('DELETE') }}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useDark, useToggle } from '@vueuse/core'
import { toast } from 'vue-sonner'
import axios from 'axios'

// UI Components
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { SwitchRoot, SwitchThumb } from 'reka-ui'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'

// Stores and utilities
import { useAuthStore } from '@/stores/auth'
import { getUploadDate } from '@/lib/utils'

const { t, locale } = useI18n()
const router = useRouter()
const authStore = useAuthStore()

// Dark mode functionality
const isDark = useDark({
  selector: 'html',
  attribute: 'class',
  valueDark: 'dark',
  valueLight: '',
})
const toggleDark = useToggle(isDark)

// Language functionality
const selectedLanguage = ref(locale.value)

const changeLanguage = (newLocale: any) => {
  locale.value = newLocale
  selectedLanguage.value = newLocale
  // You can add localStorage persistence here if needed
  localStorage.setItem('preferred-language', newLocale)
  toast.success('Language updated successfully')
}

// Delete account functionality
const showDeleteDialog = ref(false)
const isDeleting = ref(false)

const deleteAccount = async () => {
  if (!authStore.user?.id) {
    toast.error(t('DELETE_ACCOUNT_ERROR'))
    return
  }

  isDeleting.value = true

  try {
    const response = await axios.delete(`/auth/users/${authStore.user.id}`)

    if (response.status === 204) {
      toast.success(t('ACCOUNT_DELETED'))
      await authStore.logout()
      router.push('/')
    }
  } catch (error) {
    console.error('Delete account error:', error)
    toast.error(t('DELETE_ACCOUNT_ERROR'))
  } finally {
    isDeleting.value = false
    showDeleteDialog.value = false
  }
}
</script>
